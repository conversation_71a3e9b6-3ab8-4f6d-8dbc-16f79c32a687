<!-- 通用笔记模态框 (新建/编辑) -->
<div class="modal fixed z-50 left-0 top-0 w-full h-full bg-black/70 backdrop-blur-sm animate-fade-in" id="postModal">
    <div class="bg-white my-[2%] mx-auto rounded-2xl w-[90%] min-w-[320px] max-w-[80vw] lg:max-w-[1200px] shadow-2xl border border-gray-300 animate-slide-in overflow-hidden max-h-[85vh] flex flex-col">
        <!-- 模态框头部 -->
        <div class="flex justify-between items-center px-6 py-3 bg-gradient-to-br from-indigo-50/50 to-purple-50/50 flex-shrink-0">
            <button type="button" class="p-2 text-gray-500 hover:text-gray-700 transition-colors duration-200" data-dismiss="modal" title="取消">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
            <div class="flex items-center gap-2 absolute left-1/2 transform -translate-x-1/2 px-4">
                <div id="modalIcon" class="w-5 h-5 text-indigo-600">
                    <!-- 新建图标 (默认) -->
                    <svg class="w-full h-full" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                    </svg>
                </div>
                <h5 class="text-lg font-semibold text-gray-800 m-0" id="modalTitle">发布笔记</h5>
            </div>
            <button type="submit" form="postForm" id="submitButton" class="p-2 text-indigo-600 hover:text-indigo-800 transition-colors duration-200" title="保存">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
            </button>
        </div>


        <!-- 发布时间设置 -->
        <div id="editTimeWrapper" class="hidden bg-gray-50 rounded-lg px-4 md:px-6 py-2 border-b border-gray-200">
            <div class="flex items-center justify-end">
                <div class="flex items-center gap-2 w-full max-w-full">
                    <div class="relative flex-1 min-w-0">
                        <input type="text" id="postTimeInput" name="created_at" placeholder="例如: 2024-01-15 14:30"
                               autocomplete="off"
                               class="w-full px-3 py-1.5 text-sm md:text-base border border-gray-300 rounded-md transition-all duration-200 bg-white text-gray-600 focus:outline-none focus:border-indigo-500 focus:ring-1 focus:ring-indigo-100 pr-10">
                        <button type="button" id="setCurrentTimeBtn" class="absolute right-2 top-1/2 transform -translate-y-1/2 p-1 text-indigo-600 hover:text-indigo-800 transition-colors duration-200" title="设置为当前时间">
                            <svg class="w-4 h-4 md:w-5 md:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 模态框内容 -->
        <div class="px-6 pt-0 pb-6 flex-1 overflow-y-auto">
            <form id="postForm">
                <!-- 隐藏的笔记ID字段 -->
                <input type="hidden" id="postIdField" name="post_id">

                <!-- Markdown 编辑器 -->
                <div class="flex flex-col gap-0 rounded-xl border-2 border-gray-200 overflow-hidden focus-within:border-indigo-500 focus-within:shadow-sm focus-within:shadow-indigo-100 transition-all duration-300">
                    <div class="editor-container">
                        <textarea name="content" id="modalMarkdownEditor" placeholder="记录你的想法和灵感..." required
                                  class="w-full min-h-250 md:min-h-300 p-5 text-base border-none font-mono resize-y bg-white focus:outline-none"></textarea>
                    </div>
                </div>
                
                <!-- 标签编辑区域 -->
                <div class="flex flex-col gap-4">
                    <div class="flex-1 min-w-0">
                        <div class="tags-input-wrapper tags-component bg-gray-50 rounded-lg px-3 py-2">
                            <div class="flex flex-wrap gap-2 w-full" id="modalTagsContainer">
                                <!-- 标签将在这里动态添加 -->
                                <div class="flex items-center gap-2 flex-wrap flex-1 min-w-0">
                                    <input type="text" id="modalTagInput" placeholder="添加标签..."
                                           class="px-2 py-1 text-sm rounded-md transition-all duration-200 bg-white text-gray-600 focus:outline-none flex-1 min-w-0"
                                           enterkeyhint="done" inputmode="text" />
                                </div>
                            </div>
                        </div>
                        <input type="hidden" name="tags" id="modalTagsField" value="[]" />
                    </div>
                </div>
            </form>
        </div>

        
    </div>
</div>


<style>
/* 模态框通用样式 */
.modal { display: none; }
.modal.show {
    display: flex !important;
    align-items: center;
    justify-content: center;
}

/* 动画效果 */
@keyframes modalFadeIn { from { opacity: 0; } to { opacity: 1; } }
@keyframes modalSlideIn { from { transform: translateY(-50px); opacity: 0; } to { transform: translateY(0); opacity: 1; } }
.animate-fade-in { animation: modalFadeIn 0.3s ease; }
.animate-slide-in { animation: modalSlideIn 0.3s ease; }

/* 编辑器和标签输入区域样式 */
#postModal .editor-container { position: relative; border: 0; border-radius: 0 0 12px 12px; overflow: hidden; }
#postModal .tags-input-wrapper {
    display: flex; flex-wrap: wrap; align-items: center; gap: 0.5rem;
    padding: 0.5rem; border: 1px solid #e5e7eb; border-radius: 6px;
    background: white; transition: all 0.3s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    margin-top: 0.5rem;
    min-height: auto; /* 移除固定高度约束 */
    height: auto; /* 允许高度自适应 */
}
#postModal .tags-input-wrapper:focus-within {
    border-color: #6366f1;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1), 0 2px 8px rgba(99, 102, 241, 0.15);
    outline: none;
}

/* 编辑器聚焦状态 */
#postModal .border-2.border-gray-200:focus-within {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 日期时间控件流体布局优化 */
#editTimeWrapper {
    overflow: hidden; /* 防止内容溢出 */
}

#editTimeWrapper .flex.items-center.justify-end {
    justify-content: flex-start; /* 左对齐，充分利用空间 */
    overflow: hidden; /* 防止溢出 */
}

#editTimeWrapper .flex.items-center.gap-2 {
    flex-wrap: nowrap; /* 保持水平布局，不换行 */
    gap: 0.5rem; /* 合理的间距 */
    width: 100%; /* 占满宽度 */
    min-width: 0; /* 允许收缩 */
    overflow: hidden; /* 防止溢出 */
}

#postTimeInput {
    flex: 1; /* 让日期输入框占据剩余空间 */
    min-width: 0; /* 允许收缩 */
    max-width: 100%; /* 限制最大宽度 */
    box-sizing: border-box; /* 包含边框和内边距 */
    overflow: hidden; /* 处理内容溢出 */
    text-overflow: ellipsis; /* 文本溢出显示省略号 */
    white-space: nowrap; /* 不换行 */
    font-size: clamp(0.875rem, 2vw, 1rem); /* 流体字体大小 */
    padding: 0.5rem 2.5rem 0.5rem 0.75rem; /* 舒适的内边距，右侧为图标留空间 */
}

#setCurrentTimeBtn {
    position: absolute;
    right: 0.5rem;
    top: 50%;
    transform: translateY(-50%);
    padding: 0.25rem;
    flex-shrink: 0; /* 按钮不收缩 */
    width: 1.5rem;
    height: 1.5rem;
}

#setCurrentTimeBtn svg {
    width: 100%;
    height: 100%;
}

/* 修复时间输入框外边距问题 */
#postTimeInput {
    margin: 0 !important;
}

/* 移动端通用优化 */
@media (max-width: 768px) {
    #postModal textarea { padding: 1rem !important; }
    #postModal .p-6 { padding: 1rem !important; }
    #postModal .px-6 { padding-left: 1rem !important; padding-right: 1rem !important; }
    #postModal .py-5 { padding-top: 1rem !important; padding-bottom: 1rem !important; }
    #postModal .min-w-\[600px\] { min-width: unset !important; }

    /* 标签输入区域移动端优化 */
    #postModal .tags-input-wrapper {
        padding: 0.5rem !important;
    }

    #postModal .tags-input-wrapper .flex.items-center.gap-2 {
        flex-direction: row !important;
        align-items: center !important;
        gap: 0.5rem !important;
    }

#postModal #modalTagInput {
    flex: 1 !important;
    min-width: 0 !important;
    font-size: 16px !important;
    padding: 0.5rem !important;
    margin: 0 !important; /* 移除所有外边距 */
    height: auto !important; /* 允许高度自适应 */
}
}

@media (max-width: 480px) {
    #postModal textarea { padding: 0.75rem !important; }
    #postModal .p-6 { padding: 0.75rem !important; }
    #postModal .px-6 { padding-left: 0.75rem !important; padding-right: 0.75rem !important; }
    #postModal .py-5 { padding-top: 0.75rem !important; padding-bottom: 0.75rem !important; }

    /* 小屏幕设备进一步优化标签输入区域 */
    #postModal .tags-input-wrapper {
        padding: 0.25rem !important;
    }

    #postModal .tags-input-wrapper .flex.items-center.gap-2 {
        flex-direction: row !important;
        align-items: center !important;
        gap: 0.25rem !important;
    }

    #postModal #modalTagInput {
        flex: 1 !important;
        min-width: 0 !important;
        padding: 0.25rem !important;
        font-size: 16px !important;
        margin: 0 !important; /* 移除所有外边距 */
        height: auto !important; /* 允许高度自适应 */
    }
}
</style>
