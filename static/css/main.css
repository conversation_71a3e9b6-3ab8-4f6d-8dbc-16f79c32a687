/* 主样式入口文件 */

/* 引入设计令牌系统 */
@import url('./base/design-tokens.css');

/* 引入基础样式 */
@import url('./base/reset.css');

/* 引入组件样式 */
@import url('./components/base.css');
@import url('./components/modal.css');
@import url('./components/tags.css');
@import url('./components/markdown.css');

/* 引入工具类样式 */
@import url('./utilities/helpers.css');

/* 响应式优化 - 使用设计令牌系统 */

/* 模态框移动端优化 */
@media (max-width: 768px) {
    .modal .bg-white {
        margin: var(--modal-margin);
        width: 95%;
        min-width: unset !important;
        max-width: 95% !important;
        max-height: 90vh;
    }

    /* 内容区域优化 */
    .modal .p-8 {
        padding: var(--space-4);
    }

    .modal .px-8 {
        padding-left: var(--space-4);
        padding-right: var(--space-4);
    }

    /* 编辑器优化 */
    #editModal .min-h-250 {
        min-height: 180px;
    }

    /* 工具栏优化 */
    #editModal .markdown-toolbar {
        padding: var(--space-1) var(--space-2);
        gap: var(--space-1);
    }

    #editModal .toolbar-btn {
        width: 28px;
        height: 28px;
        font-size: var(--text-xs);
    }

    /* 标签区域优化 */
    #editModal .group {
        padding: var(--space-2) var(--space-3) !important;
        min-height: 48px !important;
    }

    #editModal .space-y-6 > * + * {
        margin-top: var(--space-4);
    }

    #editModal .group input {
        padding: var(--space-1) !important;
    }

    #editModal .mb-1\.5 {
        margin-bottom: var(--space-2) !important;
    }

    #editModal .gap-2 {
        gap: var(--space-1) !important;
    }

    /* 统一模态框关闭按钮样式 */
    .modal .close-button,
    .modal button[data-dismiss="modal"]:not(.custom-modal-button) {
        width: 44px !important;
        height: 44px !important;
        font-size: var(--text-xl) !important;
        min-width: 44px !important;
        min-height: 44px !important;
    }
}

/* 小屏幕设备优化 */
@media (max-width: 480px) {
    .modal .bg-white {
        margin: calc(var(--modal-margin) / 2);
        width: 98%;
        min-width: unset !important;
        max-width: 98% !important;
        max-height: 95vh;
    }

    /* 内容区域进一步减少 */
    .modal .p-8 {
        padding: var(--space-3);
    }

    .modal .px-8 {
        padding-left: var(--space-3);
        padding-right: var(--space-3);
    }

    /* 编辑器优化 */
    #editModal .min-h-250 {
        min-height: 150px;
    }

    /* 标签区域优化 */
    #editModal .group {
        padding: var(--space-1) var(--space-2) !important;
        min-height: 44px !important;
    }

    #editModal .space-y-6 > * + * {
        margin-top: var(--space-3);
    }

    #editModal .group input {
        padding: var(--space-1) !important;
        min-width: 100px !important;
    }

    #editModal .mb-1\.5 {
        margin-bottom: var(--space-1) !important;
    }

    #editModal .gap-2 {
        gap: var(--space-1) !important;
    }

    #editModal .gap-4 {
        gap: var(--space-2) !important;
    }

    /* 表单元素优化 */
    #editModal textarea {
        padding: var(--space-4) !important;
    }

    #editModal button[type="submit"] {
        padding: var(--space-3) var(--space-5);
        font-size: var(--text-base);
        min-height: 44px;
    }

    #editModal button[data-dismiss="modal"]:not(.custom-modal-button) {
        width: 44px;
        height: 44px;
        font-size: var(--text-xl);
    }

    #editModal #setCurrentTimeBtn {
        min-height: 36px;
        padding: var(--space-2) var(--space-3);
        font-size: var(--text-sm);
    }

    #editModal #editPostTime {
        min-height: 36px;
        padding: var(--space-2) var(--space-3);
        font-size: var(--text-sm);
    }

    #editModal .py-6 {
        padding-top: var(--space-4) !important;
        padding-bottom: var(--space-4) !important;
    }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
    /* 移除粘滞悬停效果 */
    #editModal .toolbar-btn:hover {
        background: transparent;
        color: var(--color-text-secondary);
    }

    /* 增加触摸目标大小 */
    #editModal .toolbar-btn {
        min-width: 44px;
        min-height: 44px;
    }

    /* 优化按钮间距 */
    #editModal .markdown-toolbar {
        gap: var(--space-2);
    }
}

/* 滚动和键盘适配优化 */
@media (max-width: 768px) {
    /* 平滑滚动 */
    #editModal .overflow-y-auto {
        -webkit-overflow-scrolling: touch;
        scroll-behavior: smooth;
    }

    /* 防止内容被虚拟键盘遮挡 */
    #editModal {
        padding-bottom: env(keyboard-inset-height, 0px);
    }

    /* 防止iOS自动缩放 */
    #editModal input,
    #editModal textarea {
        font-size: max(16px, var(--text-base));
        -webkit-appearance: none;
        border-radius: var(--border-radius-lg);
    }

    /* 改善文本选择体验 */
    #editModal textarea {
        -webkit-user-select: text;
        user-select: text;
    }
}

/* 模态框滚动容器优化 */
#editModal .flex-1.overflow-y-auto {
    min-height: 0;
    will-change: scroll-position;
    scrollbar-width: thin;
    scrollbar-color: var(--color-gray-400) var(--color-gray-100);
}

/* Webkit浏览器滚动条样式 */
#editModal .flex-1.overflow-y-auto::-webkit-scrollbar {
    width: 6px;
}

#editModal .flex-1.overflow-y-auto::-webkit-scrollbar-track {
    background: var(--color-gray-100);
    border-radius: var(--border-radius-sm);
}

#editModal .flex-1.overflow-y-auto::-webkit-scrollbar-thumb {
    background: var(--color-gray-400);
    border-radius: var(--border-radius-sm);
}

#editModal .flex-1.overflow-y-auto::-webkit-scrollbar-thumb:hover {
    background: var(--color-gray-500);
}

/* 极低窗口高度优化 */
@media (max-height: 500px) {
    .modal .bg-white {
        margin: calc(var(--modal-margin) / 2);
        max-height: 98vh;
    }

    .modal .p-8 {
        padding: var(--space-2) !important;
    }

    .modal .px-8 {
        padding-left: var(--space-2) !important;
        padding-right: var(--space-2) !important;
    }

    .modal .py-6 {
        padding-top: var(--space-2) !important;
        padding-bottom: var(--space-2) !important;
    }

    #editModal .min-h-250,
    #editModal .md\:min-h-300 {
        min-height: 120px !important;
    }

    #editModal .space-y-6 > * + * {
        margin-top: var(--space-2) !important;
    }

    #editModal .group {
        padding: var(--space-1) var(--space-2) !important;
        min-height: 40px !important;
    }

    #editModal .markdown-toolbar {
        padding: var(--space-1) !important;
        gap: 1px !important;
    }

    #editModal .toolbar-btn {
        width: 24px !important;
        height: 24px !important;
        font-size: 10px !important;
    }
}

/* 超低窗口高度优化 */
@media (max-height: 400px) {
    .modal .bg-white {
        margin: 0 auto;
        max-height: 99vh;
    }

    .modal .p-8 {
        padding: var(--space-1) !important;
    }

    .modal .px-8 {
        padding-left: var(--space-1) !important;
        padding-right: var(--space-1) !important;
    }

    .modal .py-6 {
        padding-top: var(--space-1) !important;
        padding-bottom: var(--space-1) !important;
    }

    #editModal .min-h-250,
    #editModal .md\:min-h-300 {
        min-height: 60px !important;
        max-height: 80px !important;
    }

    #editModal .space-y-6 > * + * {
        margin-top: var(--space-1) !important;
    }

    #editModal .group {
        padding: var(--space-1) !important;
        min-height: 32px !important;
    }

    /* 隐藏工具栏以节省空间 */
    #editModal .markdown-toolbar {
        display: none !important;
    }

    #editModal button[type="submit"] {
        padding: var(--space-2) var(--space-4) !important;
        font-size: var(--text-sm) !important;
        min-height: 36px !important;
    }
}

/* 标签输入框边框移除（保持向后兼容） */
#tagInput,
#editTagInput,
#modalTagInput {
    border: none !important;
    outline: none !important;
    box-shadow: none !important;
}
