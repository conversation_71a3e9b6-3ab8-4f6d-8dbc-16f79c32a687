/* 标签组件样式 - 使用设计令牌系统 */

/* 基础标签样式 */
.note-tag {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0 var(--tag-padding-x);
    height: var(--tag-height);
    font-size: var(--tag-font-size);
    font-weight: var(--font-weight-medium);
    font-family: var(--font-family-sans);
    line-height: var(--line-height-tight);
    text-decoration: none;
    border-radius: var(--tag-border-radius);
    border: var(--border-width) solid transparent;
    transition: all var(--transition-normal) var(--ease-out);
    white-space: nowrap;
    cursor: pointer;
    box-sizing: border-box;
    /* 确保标签内容垂直居中 */
    min-width: 0;
    flex-shrink: 0;
}

/* 标签文本样式 */
.note-tag .tag-text {
    position: relative;
    z-index: 10;
}

/* 普通状态标签样式 */
.note-tag--normal {
    background: linear-gradient(135deg, var(--color-primary-100) 0%, var(--color-purple-100) 100%);
    color: var(--color-primary-600);
    border-color: var(--color-primary-200);
    box-shadow: var(--shadow-sm);
}

/* 激活状态标签样式 */
.note-tag--active {
    background: linear-gradient(135deg, var(--color-primary-500) 0%, var(--color-purple-600) 100%);
    color: var(--color-text-inverse);
    border-color: var(--color-primary-600);
    box-shadow: var(--shadow-primary);
    transform: scale(1.05);
}

/* 悬停状态标签样式 */
.note-tag--normal:hover {
    background: linear-gradient(135deg, var(--color-primary-200) 0%, var(--color-purple-200) 100%);
    color: var(--color-primary-700);
    border-color: var(--color-primary-300);
    transform: scale(1.05) translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* 标签删除按钮样式 */
.note-tag .remove-tag {
    display: flex;
    align-items: center;
    justify-content: center;
    width: var(--space-4);
    height: var(--space-4);
    margin-left: var(--space-1);
    font-size: var(--text-xs);
    font-weight: var(--font-weight-bold);
    border-radius: var(--border-radius-full);
    background: rgba(255, 255, 255, 0.2);
    color: inherit;
    border: none;
    cursor: pointer;
    transition: all var(--transition-fast) var(--ease-out);
    flex-shrink: 0;
}

.note-tag .remove-tag:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.note-tag .remove-tag:focus {
    outline: 2px solid var(--color-primary-500);
    outline-offset: 1px;
}

/* 激活状态标签的指示器 */
.note-tag--active .tag-indicator {
    position: absolute;
    top: -0.25rem;
    right: -0.25rem;
    width: 0.5rem;
    height: 0.5rem;
    background: #fbbf24;
    border-radius: 50%;
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* 标签容器样式 */
.tags-container {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    width: 100%;
    min-height: var(--tag-height);
}

/* 标签输入框样式 */
.tags-container input[type="text"],
#modalTagInput {
    flex: 1 1 auto;
    min-width: 120px;
    max-width: 100%;
    height: var(--tag-height);
    padding: 0 var(--space-3);
    font-size: var(--tag-font-size);
    font-family: var(--font-family-sans);
    border: var(--border-width) solid var(--color-gray-300);
    border-radius: var(--tag-border-radius);
    background: var(--color-background);
    color: var(--color-text-primary);
    transition: all var(--transition-normal) var(--ease-out);
    box-sizing: border-box;
}

.tags-container input[type="text"]:focus,
#modalTagInput:focus {
    outline: none;
    border-color: var(--color-primary-500);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

/* 模态框中的标签输入区域样式 */
.tags-input-wrapper {
    display: flex;
    flex-direction: column;
    margin-top: var(--space-2);
    border: var(--border-width) solid var(--color-gray-300);
    border-radius: var(--border-radius-lg);
    background: var(--color-background-secondary);
    transition: all var(--transition-normal) var(--ease-out);
    width: 100%;
    box-sizing: border-box;
    box-shadow: var(--shadow-sm);
}

.tags-input-wrapper:focus-within {
    border-color: var(--color-primary-500);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1), var(--shadow-md);
    outline: none;
    background: var(--color-background);
}

/* 模态框标签容器样式 */
#modalTagsContainer {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    min-width: 0;
}

/* 流体布局优化 - 使用相对单位和clamp()函数替代媒体查询 */

/* 标签容器间隙 - 使用相对单位 */
.tags-container {
    gap: clamp(0.25rem, 1vw, 0.5rem);
}

/* 标签输入框流体宽度 */
.tags-container input[type="text"],
#modalTagInput {
    min-width: clamp(4rem, 20vw, 7.5rem);
    font-size: clamp(0.75rem, 3vw, 0.875rem);
}

/* 标签输入包装器流体内边距 */
.tags-input-wrapper {
    padding: clamp(0.5rem, 1.5vw, 0.75rem);
    gap: clamp(0.25rem, 0.75vw, 0.5rem);
}

/* 模态框标签容器间隙 */
#modalTagsContainer {
    gap: clamp(0.25rem, 1vw, 0.5rem);
}

/* 标签组件命名空间样式流体内边距 */
.tags-component .tags-input-wrapper {
    padding: clamp(0.5rem, 1.5vw, 0.75rem);
    gap: clamp(0.25rem, 0.75vw, 0.5rem);
}

/* 标签容器统一样式间隙 */
#tagsContainer,
#editTagsContainer {
    gap: clamp(0.25rem, 1vw, 0.5rem);
}

/* 确保组件布局稳定性 */
.note-tag .remove-tag,
#modalAddTagBtn {
    flex-shrink: 0;
    white-space: nowrap;
}

/* 模态框标签容器 */
#modalTagsContainer {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    width: 100%;
    min-width: 0;
    min-height: var(--tag-height);
}

/* 标签组件命名空间样式 */
.tags-component .tags-input-wrapper {
    display: flex;
    flex-direction: column;
    border: var(--border-width) solid var(--color-gray-300);
    border-radius: var(--border-radius-lg);
    background: var(--color-background-secondary);
    transition: all var(--transition-normal) var(--ease-out);
    box-shadow: var(--shadow-sm);
}

.tags-component .tags-input-wrapper:focus-within {
    border-color: var(--color-primary-500);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1), var(--shadow-md);
    outline: none;
    background: var(--color-background);
}

/* 标签反馈样式 */
.tag-feedback {
    position: absolute;
    bottom: calc(-1 * var(--space-6));
    left: 0;
    font-size: var(--text-xs);
    padding: var(--space-1) var(--space-2);
    border-radius: var(--border-radius-md);
    z-index: var(--z-tooltip);
    transition: all var(--transition-normal) var(--ease-out);
    font-weight: var(--font-weight-medium);
}

.tag-feedback--success {
    background: var(--color-success-50);
    color: var(--color-success-700);
    border: var(--border-width) solid var(--color-success-200);
}

.tag-feedback--warning {
    background: var(--color-warning-50);
    color: var(--color-warning-700);
    border: var(--border-width) solid var(--color-warning-200);
}

.tag-feedback--error {
    background: var(--color-error-50);
    color: var(--color-error-700);
    border: var(--border-width) solid var(--color-error-100);
}

.tag-feedback--info {
    background: var(--color-info-50);
    color: var(--color-info-700);
    border: var(--border-width) solid var(--color-info-200);
}

/* 标签容器统一样式 */
#tagsContainer,
#editTagsContainer {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    min-height: var(--tag-height);
}

/* 焦点和可访问性优化 */
.note-tag:focus-visible {
    outline: 2px solid var(--color-primary-500);
    outline-offset: 2px;
    z-index: 1;
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
    .note-tag {
        min-height: 44px; /* 确保触摸目标足够大 */
    }
    
    .note-tag .remove-tag {
        min-width: 44px;
        min-height: 44px;
    }
}
