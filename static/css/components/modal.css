/* 模态框组件样式 - 使用设计令牌系统 */

/* 模态框显示状态 */
.modal {
    display: none;
}

.modal.show {
    display: block !important;
}

/* 模态框动画 */
@keyframes fade-in {
    from { 
        opacity: 0; 
    }
    to { 
        opacity: 1; 
    }
}

@keyframes slide-in {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes modal-slide-in {
    from {
        transform: translateY(-50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* 动画类 */
.animate-fade-in {
    animation: fade-in var(--transition-slow) var(--ease-out);
}

.animate-slide-in {
    animation: slide-in 0.6s var(--ease-out);
}

.animate-modal-slide-in {
    animation: modal-slide-in var(--transition-slow) var(--ease-out);
}

/* 模态框宽度设置 - 桌面端 */
.modal .bg-white {
    min-width: var(--modal-min-width);
    max-width: var(--modal-max-width);
    border-radius: var(--modal-border-radius);
}

/* 统一模态框关闭按钮样式 */
.modal .close-button,
.modal button[data-dismiss="modal"]:not(.custom-modal-button) {
    width: 32px;
    height: 32px;
    min-width: 32px;
    min-height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--border-radius-full);
    transition: all var(--transition-normal) var(--ease-out);
    font-weight: var(--font-weight-normal);
    border: none;
    background: var(--color-gray-100);
    color: var(--color-text-secondary);
    cursor: pointer;
}

.modal .close-button:hover,
.modal button[data-dismiss="modal"]:not(.custom-modal-button):hover {
    transform: scale(1.1);
    background: var(--color-gray-200);
    color: var(--color-text-primary);
}

.modal .close-button:focus,
.modal button[data-dismiss="modal"]:not(.custom-modal-button):focus {
    outline: 2px solid var(--color-primary-500);
    outline-offset: 2px;
}